package com.mycompany.my_bibliotheque.dao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

public class DatabaseConnection {
    private static final String SERVER_URL = "***************************/";
    private static final String DATABASE_NAME = "bibliotheque";
    private static final String URL = SERVER_URL + DATABASE_NAME + "?autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC&connectTimeout=60000&socketTimeout=60000";
    private static final String USER = "root";
    private static final String PASSWORD = "";
    private static Connection connection = null;

    public static Connection getConnection() {
        try {
            if (connection == null || connection.isClosed()) {
                Class.forName("com.mysql.cj.jdbc.Driver");

                // First, create the database if it doesn't exist
                createDatabaseIfNotExists();

                // Then connect to the database
                connection = DriverManager.getConnection(URL, USER, PASSWORD);
                initializeDatabase();
            }
        } catch (ClassNotFoundException | SQLException e) {
            System.err.println("Erreur de connexion à la base de données: " + e.getMessage());
        }
        return connection;
    }

    public static Connection getConnection() throws SQLException {
        if (dataSource == null) {
            initializeDataSource();
        }
        return dataSource.getConnection();
    }

    private static void createDatabaseIfNotExists() throws SQLException {
        try (Connection serverConnection = DriverManager.getConnection(
                SERVER_URL + "?autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC",
                USER, PASSWORD);
             Statement stmt = serverConnection.createStatement()) {

            String createDatabaseQuery = "CREATE DATABASE IF NOT EXISTS " + DATABASE_NAME;
            stmt.executeUpdate(createDatabaseQuery);
            System.out.println("Base de données '" + DATABASE_NAME + "' créée ou vérifiée avec succès.");
        }
    }

    private static void initializeDatabase() {
        if (databaseInitialized) {
            return;
        }

        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {

            // Création des tables si elles n'existent pas
            String createLivresTable = "CREATE TABLE IF NOT EXISTS livres ("
                    + "id INT AUTO_INCREMENT PRIMARY KEY,"
                    + "titre VARCHAR(255) NOT NULL,"
                    + "auteur VARCHAR(255) NOT NULL,"
                    + "isbn VARCHAR(20) UNIQUE,"
                    + "annee_publication INT,"
                    + "categorie VARCHAR(100),"
                    + "disponible BOOLEAN DEFAULT TRUE"
                    + ")";

            String createUtilisateursTable = "CREATE TABLE IF NOT EXISTS utilisateurs ("
                    + "id INT AUTO_INCREMENT PRIMARY KEY,"
                    + "nom VARCHAR(100) NOT NULL,"
                    + "prenom VARCHAR(100) NOT NULL,"
                    + "email VARCHAR(255) UNIQUE,"
                    + "telephone VARCHAR(20),"
                    + "adresse TEXT"
                    + ")";

            String createEmpruntsTable = "CREATE TABLE IF NOT EXISTS emprunts ("
                    + "id INT AUTO_INCREMENT PRIMARY KEY,"
                    + "livre_id INT NOT NULL,"
                    + "utilisateur_id INT NOT NULL,"
                    + "date_emprunt DATE NOT NULL,"
                    + "date_retour_prevue DATE NOT NULL,"
                    + "date_retour_effective DATE,"
                    + "FOREIGN KEY (livre_id) REFERENCES livres(id),"
                    + "FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id)"
                    + ")";

            // Tables pour les étudiants et notes
            String createEtudiantsTable = "CREATE TABLE IF NOT EXISTS etudiants ("
                    + "id INT AUTO_INCREMENT PRIMARY KEY,"
                    + "nom VARCHAR(100) NOT NULL,"
                    + "prenom VARCHAR(100) NOT NULL,"
                    + "numero_etudiant VARCHAR(50) UNIQUE,"
                    + "classe VARCHAR(100)"
                    + ")";

            String createMatieresTable = "CREATE TABLE IF NOT EXISTS matieres ("
                    + "id INT AUTO_INCREMENT PRIMARY KEY,"
                    + "nom VARCHAR(100) NOT NULL,"
                    + "coefficient DOUBLE DEFAULT 1.0"
                    + ")";

            String createNotesTable = "CREATE TABLE IF NOT EXISTS notes ("
                    + "id INT AUTO_INCREMENT PRIMARY KEY,"
                    + "etudiant_id INT NOT NULL,"
                    + "matiere_id INT NOT NULL,"
                    + "valeur DOUBLE NOT NULL,"
                    + "FOREIGN KEY (etudiant_id) REFERENCES etudiants(id),"
                    + "FOREIGN KEY (matiere_id) REFERENCES matieres(id)"
                    + ")";

            stmt.executeUpdate(createLivresTable);
            stmt.executeUpdate(createUtilisateursTable);
            stmt.executeUpdate(createEmpruntsTable);
            stmt.executeUpdate(createEtudiantsTable);
            stmt.executeUpdate(createMatieresTable);
            stmt.executeUpdate(createNotesTable);

            databaseInitialized = true;
            System.out.println("Tables de la base de données initialisées avec succès.");

        } catch (SQLException e) {
            System.err.println("Erreur lors de l'initialisation de la base de données: " + e.getMessage());
        }
    }

    public static void closeDataSource() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
        }
    }
}